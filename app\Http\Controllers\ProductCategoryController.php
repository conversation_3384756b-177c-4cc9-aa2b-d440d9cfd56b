<?php

namespace App\Http\Controllers;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\ProductCategory;
use Illuminate\Http\Request;

class ProductCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            try {
                $draw = $request->get('draw');
                $start = $request->get('start');
                $length = $request->get('length');
                $searchValue = $request->get('search')['value'] ?? '';
                $orderColumn = $request->get('order')[0]['column'] ?? 0;
                $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

                $columns = ['id', 'name', 'slug', 'status', 'created_at', 'updated_at'];
                $orderColumn = $columns[$orderColumn] ?? 'id';

                $query = ProductCategory::select('id', 'name', 'slug', 'status', 'created_at', 'updated_at');

                if ($searchValue) {
                    $query->where(function ($query) use ($searchValue) {
                        $query->where('name', 'like', '%' . $searchValue . '%')
                            ->orWhere('slug', 'like', '%' . $searchValue . '%');
                    });
                }

                $totalRecords = $query->count();
                $filteredRecords = $query->count();

                $productCategories = $query->skip($start)->take($length)->orderBy($orderColumn, $orderDirection)->get();

                $data = [];
                foreach ($productCategories as $productCategory) {
                    $data[] = [
                        'id' => $productCategory->id,
                        'name' => $productCategory->name,
                        'status' => Helper::getStatusBadge($productCategory->status),
                        'created_at' => Helper::formatDate($productCategory->created_at),
                        'updated_at' => Helper::formatDate($productCategory->updated_at),
                        'actions' => Helper::getActionButtons($productCategory->id, 'product-categories')
                    ];
                }
                return response()->json(['draw' => $draw, 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (\Exception $e) {
                return response()->json(['error' => 'An error occurred while fetching data.'], 500);
            }
        }
        return view('Web.Products.Product-Category.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
