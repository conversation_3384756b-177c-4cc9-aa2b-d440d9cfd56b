

<?php $__env->startSection('title', 'Arklok Admin | Add Product Category'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                                    Add Product Category
                                    <span class="page-desc text-muted fs-7 fw-semibold pt-1">Create a new product category</span>
                                </h1>
                            </div>
                            <div>
                                <a href="<?php echo e(route('product-categories.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Categories
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                                <div class="card-title">
                                    <div class="d-flex align-items-center position-relative my-1">
                                        <i class="fas fa-plus-circle fs-3 position-absolute ms-4"></i>
                                        <h3 class="fw-bold m-0 ps-12">Product Category Information</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body pt-0">
                                <form id="product-category-form" action="<?php echo e(route('product-categories.store')); ?>" method="POST">
                                    <?php echo csrf_field(); ?>

                                    <div class="row mb-6">
                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Category Name</label>
                                        <div class="col-lg-8">
                                            <input type="text" name="name" id="category-name"
                                                   class="form-control form-control-lg form-control-solid"
                                                   placeholder="Enter category name"
                                                   value="<?php echo e(old('name')); ?>"
                                                   required maxlength="255">
                                            <div class="form-text">Enter a unique name for the product category</div>
                                        </div>
                                    </div>

                                    <div class="row mb-6">
                                        <label class="col-lg-4 col-form-label fw-semibold fs-6">Category Slug</label>
                                        <div class="col-lg-8">
                                            <input type="text" name="slug" id="category-slug"
                                                   class="form-control form-control-lg form-control-solid"
                                                   placeholder="Category slug (auto-generated)"
                                                   value="<?php echo e(old('slug')); ?>"
                                                   maxlength="255" readonly>
                                            <div class="form-text">URL-friendly version of the category name (auto-generated)</div>
                                        </div>
                                    </div>

                                    <div class="row mb-6">
                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Status</label>
                                        <div class="col-lg-8">
                                            <div class="form-check form-switch form-check-custom form-check-solid">
                                                <input class="form-check-input" type="checkbox" name="status"
                                                       id="category-status" value="1"
                                                       <?php echo e(old('status', '1') == '1' ? 'checked' : ''); ?>>
                                                <label class="form-check-label fw-semibold text-gray-400 ms-3" for="category-status">
                                                    Active
                                                </label>
                                            </div>
                                            <div class="form-text">Toggle to activate or deactivate this category</div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-lg-8 offset-lg-4">
                                            <div class="d-flex justify-content-end">
                                                <button type="reset" class="btn btn-light me-3">
                                                    <i class="fas fa-undo"></i> Reset
                                                </button>
                                                <button type="submit" id="submit-btn" class="btn btn-primary">
                                                    <span class="indicator-label">
                                                        <i class="fas fa-save"></i> Save Category
                                                    </span>
                                                    <span class="indicator-progress">
                                                        Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                                    </span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Auto-generate slug from name
            $('#category-name').on('input', function() {
                const name = $(this).val();
                const slug = name.toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
                    .replace(/\s+/g, '-') // Replace spaces with hyphens
                    .replace(/-+/g, '-') // Replace multiple hyphens with single
                    .trim('-'); // Remove leading/trailing hyphens
                $('#category-slug').val(slug);
            });

            // Initialize enhanced form with AJAX submission
            initializeEnhancedForm({
                formId: 'product-category-form',
                submitBtnId: 'submit-btn',
                successMessage: 'Product category has been created successfully!',
                redirectUrl: '<?php echo e(route("product-categories.index")); ?>',
                hasFileUpload: false,
                enableCKEditor: false,
                exitSelector: 'a[href="<?php echo e(route("product-categories.index")); ?>"]'
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\arclok_admin\resources\views/Web/Products/Product-Category/create.blade.php ENDPATH**/ ?>